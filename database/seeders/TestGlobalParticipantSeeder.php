<?php

namespace Database\Seeders;

use App\Models\GlobalParticipant;
use App\Models\Webinar;
use Carbon\Carbon;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class TestGlobalParticipantSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create test webinars
        $webinar1 = Webinar::create([
            'title' => 'Test Webinar 1',
            'speaker' => 'John Speaker',
            'join_code' => 'test001',
            'join_url' => 'https://webinar.test/join/test001',
            'user_id' => 1,
            'allow_replay' => true,
            'waiting_time' => 30,
            'join_settings' => [
                'name_required' => true,
                'email_required' => true,
                'phone_required' => true,
            ]
        ]);

        $webinar2 = Webinar::create([
            'title' => 'Test Webinar 2',
            'speaker' => 'Jane Speaker',
            'join_code' => 'test002',
            'join_url' => 'https://webinar.test/join/test002',
            'user_id' => 1,
            'allow_replay' => true,
            'waiting_time' => 30,
            'join_settings' => [
                'name_required' => true,
                'email_required' => true,
                'phone_required' => true,
            ]
        ]);

        $webinar3 = Webinar::create([
            'title' => 'Test Webinar 3',
            'speaker' => 'Bob Speaker',
            'join_code' => 'test003',
            'join_url' => 'https://webinar.test/join/test003',
            'user_id' => 1,
            'allow_replay' => true,
            'waiting_time' => 30,
            'join_settings' => [
                'name_required' => true,
                'email_required' => true,
                'phone_required' => true,
            ]
        ]);

        // Create test global participants
        GlobalParticipant::create([
            'name' => 'Test User 1',
            'email' => '<EMAIL>',
            'phone' => '0123456789',
            'ip_address' => '127.0.0.1',
            'expires_at' => Carbon::now()->addMonths(6)
        ]);

        GlobalParticipant::create([
            'name' => 'Test User 2',
            'email' => '<EMAIL>',
            'phone' => '0987654321',
            'ip_address' => '127.0.0.1',
            'expires_at' => Carbon::now()->addMonths(3)
        ]);

        // Create an expired global participant
        GlobalParticipant::create([
            'name' => 'Expired User',
            'email' => '<EMAIL>',
            'phone' => '0111111111',
            'ip_address' => '127.0.0.1',
            'expires_at' => Carbon::now()->subDays(10)
        ]);

        $this->command->info('Test data created successfully!');
        $this->command->info('Test webinars:');
        $this->command->info('- /join/test001 (Test Webinar 1)');
        $this->command->info('- /join/test002 (Test Webinar 2)');
        $this->command->info('- /join/test003 (Test Webinar 3)');
        $this->command->info('Test page: /test-global-participant');
    }
}
