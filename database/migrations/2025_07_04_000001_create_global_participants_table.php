<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('global_participants', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('email')->nullable();
            $table->string('phone')->nullable();
            $table->string('ip_address')->nullable();
            $table->timestamp('expires_at')->comment('Thời gian hết hạn (6 tháng từ lần đăng ký cuối)');
            $table->timestamps();
            
            // Indexes for faster lookups
            $table->index(['email', 'expires_at']);
            $table->index(['phone', 'expires_at']);
            $table->index('expires_at');
            
            // Unique constraints to prevent duplicates
            $table->unique('email');
            $table->unique('phone');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('global_participants');
    }
};
