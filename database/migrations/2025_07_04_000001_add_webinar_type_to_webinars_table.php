<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('webinars', function (Blueprint $table) {
            $table->enum('webinar_type', ['continuous', 'scheduled'])->default('scheduled')->after('speaker')
                ->comment('continuous: <PERSON><PERSON><PERSON> tụ<PERSON>, scheduled: <PERSON><PERSON><PERSON> lịch');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('webinars', function (Blueprint $table) {
            $table->dropColumn('webinar_type');
        });
    }
};
