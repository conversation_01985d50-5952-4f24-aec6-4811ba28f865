<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Global Participant</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Test Global Participant Feature</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Global Participants</h5>
                    </div>
                    <div class="card-body">
                        @if($globalParticipants->count() > 0)
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Phone</th>
                                        <th>Expires At</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($globalParticipants as $gp)
                                        <tr>
                                            <td>{{ $gp->id }}</td>
                                            <td>{{ $gp->name }}</td>
                                            <td>{{ $gp->email }}</td>
                                            <td>{{ $gp->phone }}</td>
                                            <td>{{ $gp->expires_at->format('d/m/Y H:i') }}</td>
                                            <td>
                                                @if($gp->isExpired())
                                                    <span class="badge bg-danger">Expired</span>
                                                @else
                                                    <span class="badge bg-success">Active</span>
                                                @endif
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        @else
                            <p>No global participants found.</p>
                        @endif
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Actions</h5>
                    </div>
                    <div class="card-body">
                        <h6>Current Cookies:</h6>
                        <ul>
                            @if(request()->cookie('global_participant_id'))
                                <li>global_participant_id: {{ request()->cookie('global_participant_id') }}</li>
                            @else
                                <li>No global_participant_id cookie</li>
                            @endif
                            
                            @if(request()->cookie('webinar_participant_id'))
                                <li>webinar_participant_id: {{ request()->cookie('webinar_participant_id') }}</li>
                            @else
                                <li>No webinar_participant_id cookie</li>
                            @endif
                        </ul>
                        
                        <h6>Session Data:</h6>
                        <ul>
                            @if(session('participant_id'))
                                <li>participant_id: {{ session('participant_id') }}</li>
                            @else
                                <li>No participant_id in session</li>
                            @endif
                        </ul>
                        
                        <h6>Test Webinars:</h6>
                        @foreach($webinars as $webinar)
                            <div class="mb-2">
                                <a href="/join/{{ $webinar->join_code }}" class="btn btn-primary btn-sm">
                                    Join {{ $webinar->title }}
                                </a>
                                <small class="text-muted">({{ $webinar->join_code }})</small>
                            </div>
                        @endforeach
                        
                        <hr>
                        
                        <h6>Actions:</h6>
                        <a href="/test-global-participant?clear_cookies=1" class="btn btn-warning btn-sm">
                            Clear Cookies
                        </a>
                        <a href="/test-global-participant?clear_session=1" class="btn btn-warning btn-sm">
                            Clear Session
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Recent Webinar Participants</h5>
                    </div>
                    <div class="card-body">
                        @if($recentParticipants->count() > 0)
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Webinar</th>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Phone</th>
                                        <th>Joined At</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($recentParticipants as $participant)
                                        <tr>
                                            <td>{{ $participant->id }}</td>
                                            <td>{{ $participant->webinar->title ?? 'N/A' }}</td>
                                            <td>{{ $participant->name }}</td>
                                            <td>{{ $participant->email }}</td>
                                            <td>{{ $participant->phone }}</td>
                                            <td>{{ $participant->joined_at ? $participant->joined_at->format('d/m/Y H:i') : 'N/A' }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        @else
                            <p>No recent participants found.</p>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
