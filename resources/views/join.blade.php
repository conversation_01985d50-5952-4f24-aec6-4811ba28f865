<?php
$is_live_learning = $webinar->livestreams["is_live_learning"] ?? 0;
?>

    <!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $webinar->title }} - Webinar</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #0891b2;
            --primary-dark: #0e7490;
            --primary-gradient: linear-gradient(135deg, #0891b2 0%, #0e7490 100%);
            --secondary-color: #06b6d4;
            --secondary-gradient: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
            --accent-color: #22d3ee;
            --accent-gradient: linear-gradient(135deg, #22d3ee 0%, #06b6d4 100%);
            --text-color: #1e293b;
            --text-light: #334155;
            --text-muted: #64748b;
            --bg-color: #f8fafc;
            --bg-gradient: linear-gradient(135deg, #0891b2 0%, #0e7490 100%);
            --card-bg: #ffffff;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --border-color: #e2e8f0;
            --border-radius: 20px;
            --border-radius-lg: 24px;
            --box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --box-shadow-lg: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-fast: all 0.15s ease-out;
        }

        body {
            background: var(--bg-color);
            background-image: radial-gradient(circle at 25% 25%, rgba(8, 145, 178, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 75% 75%, rgba(14, 116, 144, 0.1) 0%, transparent 50%),
            linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            color: var(--text-color);
            line-height: 1.7;
            min-height: 100vh;
            font-feature-settings: 'kern' 1, 'liga' 1;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .webinar-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 1rem;
            position: relative;
        }

        .webinar-card {
            width: 100%;
            max-width: 950px;
            overflow: hidden;
            position: relative;
        }



        .webinar-card::after {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: var(--primary-gradient);
            border-radius: var(--border-radius-lg);
            z-index: -1;
            opacity: 0.1;
        }

        .unified-content {
            padding: 4rem 3rem;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(247, 250, 252, 0.95) 100%);
            position: relative;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .unified-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23667eea" opacity="0.03"/><circle cx="75" cy="75" r="1" fill="%23764ba2" opacity="0.03"/><circle cx="50" cy="10" r="1" fill="%23667eea" opacity="0.03"/><circle cx="10" cy="50" r="1" fill="%23764ba2" opacity="0.03"/><circle cx="90" cy="30" r="1" fill="%23667eea" opacity="0.03"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
            opacity: 0.6;
        }

        .webinar-header {
            text-align: center;
            margin-bottom: 3.5rem;
            position: relative;
            z-index: 2;
        }

        .webinar-title {
            font-size: 35px;
            font-weight: 800;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1.5rem;
            line-height: inherit;
            letter-spacing: -0.02em;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .webinar-speaker {
            display: inline-flex;
            align-items: center;
            background: rgba(8, 145, 178, 0.1);
            padding: 1rem 2rem;
            border-radius: 50px;
            font-size: 1.1rem;
            color: var(--text-color);
            border: 2px solid rgba(8, 145, 178, 0.2);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            transition: var(--transition);
            box-shadow: 0 8px 32px rgba(8, 145, 178, 0.15);
        }

        .webinar-speaker:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(8, 145, 178, 0.25);
        }

        .speaker-title {
            font-size: 0.85rem;
            font-weight: 600;
            opacity: 0.8;
            text-transform: uppercase;
            letter-spacing: 1.2px;
            margin-right: 0.75rem;
            color: var(--text-muted);
        }

        .speaker-name {
            font-weight: 700;
            font-size: 1.1rem;
            color: var(--primary-color);
            position: relative;
        }

        .speaker-name::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 100%;
            height: 2px;
            background: var(--primary-gradient);
            border-radius: 2px;
            opacity: 0.6;
        }


        .speaker-info {
            display: flex;
            align-items: center;
            margin-bottom: 2rem;
            background-color: rgba(67, 97, 238, 0.03);
            padding: 1.25rem;
            border-radius: 12px;
            border-left: 4px solid var(--primary-color);
        }

        .speaker-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: var(--primary-gradient);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1.5rem;
            color: white;
            font-size: 2rem;
            box-shadow: 0 8px 20px rgba(67, 97, 238, 0.25);
        }

        .speaker-info h5 {
            font-weight: 600;
            font-size: 1.25rem;
            margin-bottom: 0.3rem;
        }

        .viewers-count {
            display: flex;
            align-items: center;
            font-size: 1rem;
            color: var(--light-text);
            margin-bottom: 2rem;
            padding: 1rem 1.25rem;
            background-color: rgba(0, 0, 0, 0.02);
            border-radius: 12px;
            border: 1px solid rgba(0, 0, 0, 0.03);
        }

        .viewers-count i {
            color: var(--primary-color);
            margin-right: 0.75rem;
            font-size: 1.2rem;
        }

        #viewerCount {
            font-weight: 600;
            color: var(--text-color);
        }

        .countdown-container {
            margin: 3rem 0;
            text-align: center;
            background: rgba(8, 145, 178, 0.05);
            padding: 2.5rem;
            border-radius: var(--border-radius);
            border: 2px dashed rgba(8, 145, 178, 0.2);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }

        .countdown-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(8, 145, 178, 0.02) 50%, transparent 70%);
            pointer-events: none;
        }

        .countdown-container h4 {
            margin-bottom: 1.5rem;
            font-weight: 600;
            color: var(--primary-dark);
            position: relative;
            display: inline-block;
        }

        .countdown-container h4::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 50px;
            height: 3px;
            background: var(--primary-gradient);
            border-radius: 10px;
        }

        .countdown {
            display: flex;
            justify-content: center;
            gap: 1.25rem;
        }

        .countdown-item {
            background: rgba(255, 255, 255, 0.9);
            border-radius: var(--border-radius);
            padding: 1.5rem 1.75rem;
            min-width: 100px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
            border: 2px solid rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            transition: var(--transition);
        }

        .countdown-item:hover {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 15px 35px rgba(8, 145, 178, 0.2);
        }

        .countdown-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-gradient);
            border-radius: var(--border-radius) var(--border-radius) 0 0;
        }

        .countdown-value {
            font-size: 2.25rem;
            font-weight: 700;
            color: var(--primary-color);
            line-height: 1;
        }

        .countdown-label {
            font-size: 0.8rem;
            font-weight: 500;
            color: var(--light-text);
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-top: 0.5rem;
        }

        .cta-button {
            background: var(--primary-gradient);
            border: none;
            color: white;
            padding: 1.25rem 2.5rem;
            font-weight: 600;
            font-size: 1.1rem;
            border-radius: var(--border-radius);
            box-shadow: 0 10px 25px rgba(8, 145, 178, 0.3);
            transition: var(--transition);
            position: relative;
            overflow: hidden;
            z-index: 1;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .cta-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: all 0.8s ease;
            z-index: -1;
        }

        .cta-button:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow: 0 20px 40px rgba(8, 145, 178, 0.4);
            color: white;
        }

        .cta-button:hover::before {
            left: 100%;
        }

        .cta-button:active {
            transform: translateY(-2px) scale(0.98);
        }

        .secondary-button {
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid var(--border-color);
            color: var(--text-color);
            padding: 1.25rem 2.5rem;
            font-weight: 600;
            border-radius: var(--border-radius);
            transition: var(--transition);
            font-size: 1.1rem;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .secondary-button:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
            background: rgba(8, 145, 178, 0.1);
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 10px 25px rgba(8, 145, 178, 0.15);
        }

        .secondary-button:active {
            transform: translateY(-1px) scale(0.98);
        }

        .form-section {
            max-width: 850px;
            /*margin: 2.5rem auto;*/
            display: none;
            background: rgba(255, 255, 255, 0.95);
            border-radius: var(--border-radius-lg);
            padding: 3.5rem;
            box-shadow: var(--box-shadow-lg);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
        }

        .form-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: var(--primary-gradient);
            border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
        }

        .form-section::after {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: var(--primary-gradient);
            border-radius: var(--border-radius-lg);
            z-index: -1;
            opacity: 0.1;
        }

        .form-section.show-direct {
            display: block !important;
            animation: slideInUp 0.8s cubic-bezier(0.16, 1, 0.3, 1);
        }

        .form-section.show-direct .form-header {
            animation: fadeInDown 0.6s ease 0.2s both;
        }

        .form-section.show-direct .form-grid {
            animation: fadeInUp 0.6s ease 0.4s both;
        }

        .form-section.show-direct .cta-button {
            animation: fadeInUp 0.6s ease 0.6s both;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .form-section.active {
            display: block;
            animation: slideInUp 0.8s cubic-bezier(0.16, 1, 0.3, 1);
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(40px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        /* Floating animation for decorative elements */
        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-10px);
            }
        }

        /* Gradient animation */
        @keyframes gradientShift {
            0% {
                background-position: 0% 50%;
            }
            50% {
                background-position: 100% 50%;
            }
            100% {
                background-position: 0% 50%;
            }
        }

        /* Add floating animation to certain elements */
        .webinar-speaker {
            animation: float 6s ease-in-out infinite;
        }

        .form-header .icon-wrapper {
            animation: float 4s ease-in-out infinite;
        }

        /* Professional blue theme animations */
        @keyframes oceanWave {
            0%, 100% {
                transform: translateX(0) translateY(0);
            }
            25% {
                transform: translateX(5px) translateY(-3px);
            }
            50% {
                transform: translateX(0) translateY(-5px);
            }
            75% {
                transform: translateX(-5px) translateY(-3px);
            }
        }

        /* Webinar Ended Section Styles */
        .webinar-ended-section {
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
        }

        .webinar-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
            padding: 2rem;
            background: rgba(8, 145, 178, 0.05);
            border-radius: var(--border-radius);
            border: 1px solid rgba(8, 145, 178, 0.1);
        }

        .info-item {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(8, 145, 178, 0.1);
            transition: var(--transition);
        }

        .info-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(8, 145, 178, 0.15);
        }

        .info-item i {
            font-size: 1.5rem;
            margin-top: 0.25rem;
            opacity: 0.8;
        }

        .info-item div {
            flex: 1;
        }

        .info-item strong {
            display: block;
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 0.5rem;
        }

        .info-item p {
            margin: 0;
            color: var(--text-secondary);
            font-size: 0.95rem;
        }

        .form-header {
            text-align: center;
            margin-bottom: 3rem;
            position: relative;
        }

        .form-header .icon-wrapper {
            display: inline-block;
            width: 100px;
            height: 100px;
            background: var(--primary-gradient);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            box-shadow: 0 15px 40px rgba(8, 145, 178, 0.4);
            position: relative;
            transition: var(--transition);
            border: 3px solid rgba(255, 255, 255, 0.2);
        }

        .form-header .icon-wrapper:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 20px 50px rgba(8, 145, 178, 0.5);
        }

        .form-header .icon-wrapper::before {
            content: '';
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            background: var(--primary-gradient);
            border-radius: 50%;
            opacity: 0.2;
            animation: pulse 2s infinite;
        }

        .form-header .icon-wrapper i {
            color: white;
            font-size: 2.2rem;
            position: relative;
            z-index: 1;
        }

        .form-header h3 {
            font-weight: 800;
            color: var(--text-color);
            margin-bottom: 1rem;
            font-size: 2rem;
            background: linear-gradient(135deg, var(--text-color), var(--primary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .form-header p {
            color: var(--light-text);
            font-size: 1.1rem;
            margin-bottom: 0;
            line-height: 1.6;
            max-width: 500px;
            margin-left: auto;
            margin-right: auto;
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .form-grid .input-group {
            margin-bottom: 0;
        }

        .form-grid .input-group.full-width {
            grid-column: 1 / -1;
        }

        .webinar-description {
            background: rgba(8, 145, 178, 0.05);
            border-radius: var(--border-radius);
            padding: 2rem;
            margin-bottom: 2.5rem;
            border-left: 5px solid var(--primary-color);
            font-size: 1.1rem;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(8, 145, 178, 0.1);
            position: relative;
            overflow: hidden;
        }

        .webinar-description::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(8, 145, 178, 0.02) 50%, transparent 70%);
            pointer-events: none;
        }

        .webinar-description p:last-child {
            margin-bottom: 0;
        }

        .input-group {
            margin-bottom: 1.75rem;
        }

        .input-group label {
            display: block;
            margin-bottom: 0.75rem;
            font-weight: 600;
            color: var(--text-color);
            font-size: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .input-group label i {
            opacity: 0.7;
            transition: var(--transition-fast);
        }

        .input-group:focus-within label i {
            opacity: 1;
            color: var(--primary-color);
        }

        .input-group input {
            width: 100%;
            padding: 1.5rem 1.75rem;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 1.05rem;
            transition: var(--transition);
            background: rgba(255, 255, 255, 0.8);
            color: var(--text-color);
            position: relative;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            font-weight: 500;
        }

        .input-group input::placeholder {
            color: var(--text-muted);
            transition: var(--transition-fast);
            font-weight: 400;
        }

        .input-group input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(8, 145, 178, 0.15);
            outline: none;
            background: rgba(255, 255, 255, 0.95);
            transform: translateY(-3px) scale(1.02);
        }

        .input-group input:focus::placeholder {
            opacity: 0.6;
            transform: translateY(-2px);
        }

        .input-group input:hover {
            border-color: rgba(8, 145, 178, 0.5);
            background: rgba(255, 255, 255, 0.9);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(8, 145, 178, 0.1);
        }

        .required::after {
            content: " *";
            color: var(--secondary-color);
        }

        .note {
            font-size: 0.85rem;
            color: var(--light-text);
            margin-top: 1.5rem;
            border-radius: 8px;
        }

        .live-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.75rem 1.5rem;
            background: var(--secondary-gradient);
            color: white;
            font-size: 0.9rem;
            font-weight: 700;
            border-radius: 50px;
            margin-bottom: 1.5rem;
            text-transform: uppercase;
            letter-spacing: 1.2px;
            animation: pulse 2s infinite;
            box-shadow: 0 8px 25px rgba(6, 182, 212, 0.4);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.2);
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 8px 25px rgba(6, 182, 212, 0.4), 0 0 0 0 rgba(6, 182, 212, 0.4);
                transform: scale(1);
            }
            50% {
                box-shadow: 0 8px 25px rgba(6, 182, 212, 0.4), 0 0 0 15px rgba(6, 182, 212, 0);
                transform: scale(1.05);
            }
            100% {
                box-shadow: 0 8px 25px rgba(6, 182, 212, 0.4), 0 0 0 0 rgba(6, 182, 212, 0);
                transform: scale(1);
            }
        }

        .viewer-joined {
            animation: slideUp 0.5s ease;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.75rem 1.5rem;
            border-radius: 50px;
            font-weight: 600;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 1.5rem;
        }

        .status-badge.live {
            background: var(--secondary-gradient);
            color: white;
            animation: pulse 2s infinite;
            box-shadow: 0 8px 25px rgba(6, 182, 212, 0.4);
            border: 2px solid rgba(255, 255, 255, 0.2);
        }

        .status-badge.replay {
            background: var(--accent-gradient);
            color: white;
            box-shadow: 0 8px 25px rgba(34, 211, 238, 0.4);
            border: 2px solid rgba(255, 255, 255, 0.2);
        }

        .status-badge.waiting {
            background: linear-gradient(135deg, #fbbf24, #f59e0b);
            color: white;
            box-shadow: 0 8px 25px rgba(251, 191, 36, 0.4);
            border: 2px solid rgba(255, 255, 255, 0.2);
        }

        .status-badge.ended {
            background: linear-gradient(135deg, #64748b, #475569);
            color: white;
            box-shadow: 0 8px 25px rgba(100, 116, 139, 0.4);
            border: 2px solid rgba(255, 255, 255, 0.2);
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .webinar-container {
                padding: 1rem 0.5rem;
            }

            .webinar-card {
                border-radius: var(--border-radius);
                margin: 0.5rem;
            }

            .unified-content {
                padding: 2.5rem 1.5rem;
            }

            .webinar-title {
                font-size: 2.2rem;
                line-height: 1.2;
            }

            .webinar-speaker {
                padding: 0.75rem 1.25rem;
                font-size: 0.95rem;
                flex-direction: column;
                gap: 0.25rem;
            }

            .speaker-avatar {
                width: 60px;
                height: 60px;
                font-size: 1.5rem;
            }

            .countdown-item {
                min-width: 70px;
                padding: 0.75rem 0.5rem;
            }

            .countdown-value {
                font-size: 1.5rem;
            }

            .form-grid {
                grid-template-columns: 1fr;
                gap: 1.25rem;
            }

            .webinar-info-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
                padding: 1.5rem;
            }

            .webinar-ended-section {
                padding: 1rem;
            }

            .info-item {
                padding: 0.75rem;
            }

            .form-section {
                padding: 2rem 1.5rem;
                margin: 1rem 0.5rem;
                border-radius: var(--border-radius);
            }

            .form-header .icon-wrapper {
                width: 70px;
                height: 70px;
            }

            .form-header .icon-wrapper i {
                font-size: 1.75rem;
            }

            .cta-button, .secondary-button {
                padding: 1rem 1.5rem;
                font-size: 1rem;
            }

            .input-group input {
                padding: 1.25rem 1.5rem;
                font-size: 1rem;
            }
        }

        @media (max-width: 480px) {
            .webinar-title {
                font-size: 1.8rem;
            }

            .countdown {
                gap: 0.75rem;
            }

            .countdown-item {
                min-width: 60px;
                padding: 0.5rem;
            }

            .countdown-value {
                font-size: 1.25rem;
            }

            .countdown-label {
                font-size: 0.7rem;
            }
        }

        /* WebView Detection Modal Styles */
        .webview-modal-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 10000;
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
        }

        .webview-modal {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 20px;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            max-width: 400px;
            width: 90%;
            overflow: hidden;
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translate(-50%, -60%) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1);
            }
        }

        .webview-modal-header {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            padding: 20px;
            text-align: center;
            color: white;
        }

        .webview-modal-logo {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .webview-modal-logo i {
            font-size: 24px;
        }

        .webview-modal-subtitle {
            font-size: 14px;
            opacity: 0.9;
            font-weight: 500;
        }

        .webview-modal-body {
            padding: 30px 25px;
            text-align: center;
        }

        .webview-modal-title {
            font-size: 20px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 15px;
            line-height: 1.3;
        }

        .webview-modal-description {
            font-size: 16px;
            color: #6b7280;
            margin-bottom: 25px;
            line-height: 1.5;
        }

        .webview-modal-button {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
        }

        .webview-modal-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
        }

        .webview-modal-button:active {
            transform: translateY(0);
        }

        /* Mobile responsive */
        @media (max-width: 480px) {
            .webview-modal {
                max-width: 350px;
                width: 95%;
            }

            .webview-modal-body {
                padding: 25px 20px;
            }

            .webview-modal-title {
                font-size: 18px;
            }

            .webview-modal-description {
                font-size: 15px;
            }
        }
    </style>
</head>
<body>
<!-- WebView Detection Modal -->
<div id="webview-modal-overlay" class="webview-modal-overlay">
    <div class="webview-modal">
        <div class="webview-modal-header">
            <div class="webview-modal-logo">
                <i class="fas fa-exclamation-circle"></i>
                <span>Thông báo hệ thống</span>
            </div>
        </div>
        <div class="webview-modal-body">
            <h3 class="webview-modal-title">Trải nghiệm trên trình duyệt hiện tại chưa được tối ưu</h3>
            <p class="webview-modal-description">Vui lòng sử dụng trình duyệt <strong>Safari</strong> để có trải nghiệm tốt nhất về khóa học.</p>
            <button id="open-in-browser" class="webview-modal-button">
                Tiếp tục với Safari
            </button>
        </div>
    </div>
</div>

<div class="webinar-container">
    <div >


        <!-- Content continues in unified layout -->
        @if (session('error'))
            <div class="alert alert-danger mb-4">
                {{ session('error') }}
            </div>
        @endif

        @php
            // Xác định xem có nên hiển thị form trực tiếp không
            $showFormDirect = false;
            $formType = 'register'; // 'register' hoặc 'login'
            $statusBadge = '';
            $formTitle = 'Đăng ký tham gia';
            $formDescription = 'Hoàn thành thông tin dưới đây để tham gia webinar';

            if($is_live_learning == 1) {
                $showFormDirect = true;
                $formType = 'login';
                $statusBadge = 'live';
                $formTitle = 'Đăng nhập lớp học';
                $formDescription = 'Đăng nhập để tham gia lớp học trực tuyến';
            } elseif(isset($isLive) && $isLive) {
                $showFormDirect = true;
                $statusBadge = 'live';
                $formTitle = 'Tham gia Webinar Live';
                $formDescription = 'Webinar đang phát trực tiếp! Đăng ký ngay để tham gia';
            } elseif(isset($webinarActive) && $webinarActive) {
                $showFormDirect = true;
                $statusBadge = 'live';
                $formTitle = 'Tham gia Webinar Live';
                $formDescription = 'Webinar đang phát trực tiếp! Đăng ký ngay để tham gia';
            } elseif(isset($inWaitingPeriod) && $inWaitingPeriod) {
                $showFormDirect = true;
                $statusBadge = 'waiting';
                $formTitle = 'Vào phòng chờ';
                $formDescription = 'Phòng chờ đã mở! Đăng ký để tham gia webinar';
            } elseif(isset($is_live_stream) && $is_live_stream == 1) {
                $showFormDirect = true;
                $statusBadge = 'live';
                $formTitle = 'Tham gia Webinar Live';
                $formDescription = 'Webinar đang phát trực tiếp! Đăng ký ngay để tham gia';
            } elseif($webinar->video_path && isset($showRecording) && $showRecording) {
                $showFormDirect = true;
                $statusBadge = 'replay';
                $formTitle = 'Xem bản ghi Webinar';
                $formDescription = 'Bản ghi webinar đã sẵn sàng. Đăng ký để xem ngay';
            }
        @endphp



        @if($showFormDirect)
            <!-- Form hiển thị trực tiếp -->
            <div id="direct-form" class="form-section show-direct">
                <div class="webinar-header">
                    <h1 class="webinar-title">{{ $webinar->title }}</h1>
                    <div class="webinar-speaker">
                        <span class="speaker-title">HOST:</span>
                        <span class="speaker-name">{{ $webinar->speaker }}</span>
                    </div>
                </div>
                <div class="form-header">
                    @if($statusBadge && $statusBadge != 'replay')
                        <div class="status-badge {{ $statusBadge }}">
                            @if($statusBadge == 'live')
                                <i class="fas fa-circle me-2"></i>LIVE
                            @elseif($statusBadge == 'waiting')
                                <i class="fas fa-clock me-2"></i>PHÒNG CHỜ
                            @endif
                        </div>
                    @endif

                    {{--                        <div class="icon-wrapper">--}}
                    {{--                            @if($formType == 'login')--}}
                    {{--                                <i class="fas fa-sign-in-alt"></i>--}}
                    {{--                            @else--}}
                    {{--                                <i class="fas fa-user-edit"></i>--}}
                    {{--                            @endif--}}
                    {{--                        </div>--}}
                    @if($statusBadge != 'replay')
                        <h3>{{ $formTitle }}</h3>
                        <p>{{ $formDescription }}</p>
                    @endif
                </div>

                @if ($errors->any())
                    <div class="alert alert-danger shadow-sm border-0 mb-4">
                        <ul class="mb-0">
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                @if($formType == 'login')
                    <!-- Form đăng nhập cho lớp học -->
                    <form action="{{route('join.login.learning',$webinar->join_code)}}" method="POST">
                        @csrf
                        <div class="form-grid">
                            <div class="input-group full-width">
                                <label for="login-email" class="required">
                                    <i class="fas fa-envelope text-primary me-2 opacity-75"></i>Email
                                </label>
                                <input type="email" id="login-email" name="email" value="{{ old('email') }}"
                                       placeholder="Nhập địa chỉ email của bạn" required>
                            </div>
                            <div class="input-group full-width">
                                <label for="login-password" class="required">
                                    <i class="fas fa-lock text-primary me-2 opacity-75"></i>Mật khẩu
                                </label>
                                <input type="password" id="login-password" name="password"
                                       placeholder="Nhập mật khẩu" required>
                            </div>
                        </div>
                        <div class="mt-4">
                            <button type="submit" class="btn cta-button w-100">
                                <i class="fas fa-sign-in-alt me-2"></i> Truy cập lớp học
                            </button>
                        </div>
                    </form>
                @else
                    <!-- Form đăng ký cho webinar -->
                    <form action="/join/{{ $webinar->join_code }}/form" method="POST" id="direct-join-form">
                        @csrf
                        <input type="hidden" name="debug_info" value="form-submission-{{ time() }}">

                        <!-- UTM Tracking Parameters -->
                        <input type="hidden" name="utm_source" value="{{ request()->get('utm_source') }}">
                        <input type="hidden" name="utm_medium" value="{{ request()->get('utm_medium') }}">
                        <input type="hidden" name="utm_campaign" value="{{ request()->get('utm_campaign') }}">
                        <input type="hidden" name="utm_term" value="{{ request()->get('utm_term') }}">
                        <input type="hidden" name="utm_content" value="{{ request()->get('utm_content') }}">

                        <div class="form-grid">
                            <div class="input-group">
                                <label for="direct-name"
                                       class="@if(isset($webinar->join_settings['name_required']) && $webinar->join_settings['name_required']) required @endif">
                                    <i class="fas fa-user text-primary me-2 opacity-75"></i>Họ và tên
                                </label>
                                <input type="text" id="direct-name" name="name" value="{{ old('name') }}"
                                       placeholder="Nhập họ và tên của bạn"
                                       @if(isset($webinar->join_settings['name_required']) && $webinar->join_settings['name_required']) required @endif>
                            </div>

                            <div class="input-group">
                                <label for="direct-phone"
                                       class="@if(isset($webinar->join_settings['phone_required']) && $webinar->join_settings['phone_required']) required @endif">
                                    <i class="fas fa-phone-alt text-primary me-2 opacity-75"></i>Số điện thoại
                                </label>
                                <input type="tel" id="direct-phone" name="phone" value="{{ old('phone') }}"
                                       placeholder="Nhập số điện thoại của bạn"
                                       @if(isset($webinar->join_settings['phone_required']) && $webinar->join_settings['phone_required']) required @endif>
                            </div>

                            <div class="input-group full-width">
                                <label for="direct-email"
                                       class="@if(isset($webinar->join_settings['email_required']) && $webinar->join_settings['email_required']) required @endif">
                                    <i class="fas fa-envelope text-primary me-2 opacity-75"></i>Email
                                </label>
                                <input type="email" id="direct-email" name="email" value="{{ old('email') }}"
                                       placeholder="Nhập địa chỉ email của bạn"
                                       @if(isset($webinar->join_settings['email_required']) && $webinar->join_settings['email_required']) required @endif>
                            </div>
                        </div>

                        <div class="note mb-4">
                            <i class="fas fa-info-circle me-2"></i>
                            <span class="text-danger">*</span> Trường bắt buộc phải nhập
                        </div>

                        <div class="mt-4">
                            @if(setting('recaptcha_enabled')==="1")
                                <button class="btn cta-button w-100 g-recaptcha"
                                        data-sitekey="{{ setting('recaptcha_site_key') }}"
                                        data-callback='onDirectJoinSubmit'
                                        data-action='submit'>
                                    <i class="fas fa-sign-in-alt me-2"></i> Tham gia ngay
                                </button>
                            @else
                                <button type="submit" class="btn cta-button w-100">
                                    <i class="fas fa-sign-in-alt me-2"></i> Tham gia ngay
                                </button>
                            @endif
                        </div>
                    </form>
                @endif
            </div>
        @else
            <div class="viewers-count" @if(!isset($isLive) || !$isLive) style="display: none;" @endif>
                <i class="fas fa-users me-2"></i>
                <span id="viewerCount">{{ $webinar->virtual_viewers }}</span> <span class="ms-1">người đang tham gia webinar này</span>
            </div>

            @if(request()->has('debug'))
                <div class="stream-duration">
                    <i class="fas fa-broadcast-tower"></i> Đang phát: <span id="stream-duration">00:00:00</span>
                </div>
            @endif

            <div id="webinar-details">

                @if(isset($is_live_stream) && $is_live_stream==1)
                    <div class="text-center mb-4">
                        <span class="live-badge"><i class="fas fa-circle me-2"></i>Live</span>
                        <h4 class="fw-bold">Webinar đang phát trực tiếp!</h4>
                        <p class="text-muted">Đăng ký ngay để tham gia buổi webinar</p>
                    </div>


                    <div class="webinar-description">
                        <p>
                            <i class="fas fa-broadcast-tower me-2 text-primary"></i> Webinar đang phát trực
                            tiếp!
                            Đăng ký để tham gia ngay.
                        </p>
                    </div>

                    <div class="text-center mt-4">
                        <button id="registerButton" class="btn cta-button btn-lg">
                            <i class="fas fa-user-plus me-2"></i> Đăng Ký Tham Gia
                        </button>
                    </div>
                @elseif(!$is_live_stream && !$webinar->allow_replay && $startTimeWebinar)
                    <!-- Webinar Not Live and No Replay - Show Next Schedule with Countdown -->
                    <div class="webinar-ended-section">
                        <div class="webinar-header">
                            <h1 class="webinar-title">{{ $webinar->title }}</h1>
                            <div class="webinar-speaker">
                                <span class="speaker-title">HOST:</span>
                                <span class="speaker-name">{{ $webinar->speaker }}</span>
                            </div>
                        </div>

                        <div class="form-header">
                            <div class="status-badge waiting">
                                <i class="fas fa-clock me-2"></i>CHƯA PHÁT SÓNG
                            </div>
                            <h3>Chưa tới lịch phát sóng</h3>
                            <p>Webinar chưa bắt đầu. Vui lòng quay lại vào thời gian được lên lịch.</p>
                        </div>

                        <!-- Countdown to Next Schedule -->
                        @php
                            $nextScheduleTime = $startTimeWebinar;
                            $waitingTime = $webinar->waiting_time ?? 30;
                            $waitingStartTime = $nextScheduleTime->copy()->subMinutes($waitingTime);
                        @endphp

                        <div class="countdown-container">
                            <h4>Lịch phát sóng sắp tới:</h4>
                            <div class="webinar-info-grid mb-4">
                                <div class="info-item">
                                    <i class="fas fa-calendar-alt text-primary"></i>
                                    <div>
                                        <strong>Ngày phát sóng</strong>
                                        <p>{{ $nextScheduleTime->format('d/m/Y') }}</p>
                                    </div>
                                </div>
                                <div class="info-item">
                                    <i class="fas fa-clock text-primary"></i>
                                    <div>
                                        <strong>Thời gian</strong>
                                        <p>{{ $nextScheduleTime->format('H:i') }}</p>
                                    </div>
                                </div>
                            </div>

                            <h4>Đếm ngược đến khi mở phòng chờ:</h4>
                            <div class="countdown" id="countdown"
                                 data-target="{{ $waitingStartTime->timestamp }}">
                                <div class="countdown-item">
                                    <div class="countdown-value" id="days">--</div>
                                    <div class="countdown-label">Ngày</div>
                                </div>
                                <div class="countdown-item">
                                    <div class="countdown-value" id="hours">--</div>
                                    <div class="countdown-label">Giờ</div>
                                </div>
                                <div class="countdown-item">
                                    <div class="countdown-value" id="minutes">--</div>
                                    <div class="countdown-label">Phút</div>
                                </div>
                                <div class="countdown-item">
                                    <div class="countdown-value" id="seconds">--</div>
                                    <div class="countdown-label">Giây</div>
                                </div>
                            </div>
                        </div>

                        @if($webinar->description)
                        <div class="webinar-description">
                            <h4><i class="fas fa-info-circle me-2"></i>Về buổi webinar</h4>
                            <p>{{ $webinar->description }}</p>
                        </div>
                        @endif

                        <div class="alert alert-warning text-center">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Thông báo</strong>
                            <p class="mb-0 mt-2">Webinar chưa bắt đầu. Phòng chờ sẽ mở {{ $waitingTime }} phút trước giờ phát sóng. Hiện tại chưa thể đăng ký tham gia.</p>
                        </div>

                        <div class="text-center mt-4">
                            <a href="{{ url('/') }}" class="btn secondary-button">
                                <i class="fas fa-home me-2"></i> Quay về trang chủ
                            </a>
                        </div>
                    </div>
                @elseif(isset($webinarEnded) && $webinarEnded && !$webinar->allow_replay)
                    <!-- Webinar Ended - Beautiful Layout -->
                    <div class="webinar-ended-section">
                        <div class="webinar-header">
                            <h1 class="webinar-title">{{ $webinar->title }}</h1>
                            <div class="webinar-speaker">
                                <span class="speaker-title">HOST:</span>
                                <span class="speaker-name">{{ $webinar->speaker }}</span>
                            </div>
                        </div>

                        <div class="form-header">
                            <div class="status-badge ended">
                                <i class="fas fa-check-circle me-2"></i>ĐÃ KẾT THÚC
                            </div>
                            <h3>Buổi webinar đã kết thúc</h3>
                            <p>Cảm ơn bạn đã quan tâm đến buổi webinar này. Buổi học đã diễn ra thành công.</p>
                        </div>

                        <!-- Webinar Information -->
                        <div class="webinar-info-grid">
                            <div class="info-item">
                                <i class="fas fa-calendar-alt text-primary"></i>
                                <div>
                                    <strong>Ngày tổ chức</strong>
                                    <p>{{ $displayInfo['displayDate'] ?? 'Chưa có lịch' }}</p>
                                </div>
                            </div>
                            <div class="info-item">
                                <i class="fas fa-clock text-primary"></i>
                                <div>
                                    <strong>Thời gian</strong>
                                    <p>{{ $displayInfo['displayTime'] ?? 'Chưa xác định' }}</p>
                                </div>
                            </div>
                            @if($webinar->duration)
                            <div class="info-item">
                                <i class="fas fa-hourglass-half text-primary"></i>
                                <div>
                                    <strong>Thời lượng</strong>
                                    <p>{{ $webinar->duration }} phút</p>
                                </div>
                            </div>
                            @endif
                            <div class="info-item">
                                <i class="fas fa-user-tie text-primary"></i>
                                <div>
                                    <strong>Diễn giả</strong>
                                    <p>{{ $webinar->speaker }}</p>
                                </div>
                            </div>
                        </div>

                        @if($webinar->description)
                        <div class="webinar-description">
                            <h4><i class="fas fa-info-circle me-2"></i>Về buổi webinar</h4>
                            <p>{{ $webinar->description }}</p>
                        </div>
                        @endif

                        <div class="alert alert-info text-center">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Thông báo</strong>
                            <p class="mb-0 mt-2">Buổi webinar này đã diễn ra và hiện tại không được phép xem lại.</p>
                        </div>

                        <div class="text-center mt-4">
                            <a href="{{ url('/') }}" class="btn secondary-button">
                                <i class="fas fa-home me-2"></i> Quay về trang chủ
                            </a>
                        </div>
                    </div>
                @elseif($webinarActive)
                    @php
                        $now = \Carbon\Carbon::now();
                        // Định nghĩa $scheduledTime từ currentSchedule
                        $scheduledTime = isset($currentSchedule) ?
                            \Carbon\Carbon::parse($currentSchedule['date'] . ' ' . $currentSchedule['time']) :
                            \Carbon\Carbon::now()->addDay(); // Giá trị mặc định

                        // Khởi tạo biến mặc định
                        $countdownTitle = "Đếm ngược:";
                        $buttonText = "Tham gia";
                        $targetTime = null;
                        $showCountdown = false;
                        $isLive = false;

                        if($inWaitingPeriod) {
                            $targetTime = $scheduledTime;
                            $countdownTitle = "Webinar bắt đầu trong:";
                            $buttonText = "Vào phòng chờ";
                            $showCountdown = true;
                        } else if(isset($scheduledTime) && $scheduledTime->isFuture()) {
                            $targetTime = $scheduledTime->copy()->subMinutes($webinar->waiting_time);
                            $countdownTitle = "Phòng chờ mở trong:";
                            $buttonText = "Nhắc nhở tôi";
                            $showCountdown = true;
                        } else {
                            $isLive = true;
                        }
                    @endphp

                    @if($isLive)
                        <div class="text-center mb-4">
                            <span class="live-badge"><i class="fas fa-circle me-2"></i>Live</span>
                            <h4 class="fw-bold">Webinar đang phát trực tiếp!</h4>
                            <p class="text-muted">Đăng ký ngay để tham gia buổi webinar</p>
                        </div>
                    @endif

                    @if($showCountdown)
                        <div class="countdown-container">
                            <h4>{{ $countdownTitle }}</h4>
                            <div class="countdown" id="countdown"
                                 data-target="{{ isset($targetTime) && is_object($targetTime) ? $targetTime->timestamp : '' }}">
                                <div class="countdown-item">
                                    <div class="countdown-value" id="days">--</div>
                                    <div class="countdown-label">Ngày</div>
                                </div>
                                <div class="countdown-item">
                                    <div class="countdown-value" id="hours">--</div>
                                    <div class="countdown-label">Giờ</div>
                                </div>
                                <div class="countdown-item">
                                    <div class="countdown-value" id="minutes">--</div>
                                    <div class="countdown-label">Phút</div>
                                </div>
                                <div class="countdown-item">
                                    <div class="countdown-value" id="seconds">--</div>
                                    <div class="countdown-label">Giây</div>
                                </div>
                            </div>
                        </div>
                    @endif

                    <div class="webinar-description">
                        <p>
                            @if($inWaitingPeriod)
                                <i class="fas fa-info-circle me-2 text-primary"></i> Phòng chờ đã mở! Bạn cần đăng
                                ký để
                                tham gia.
                            @elseif(isset($webinarActive) && $webinarActive)
                                <i class="fas fa-broadcast-tower me-2 text-primary"></i> Webinar đang phát trực
                                tiếp!
                                Đăng ký để tham gia ngay.
                            @elseif($webinar->video_path && isset($showRecording) && $showRecording)
                                <i class="fas fa-play-circle me-2 text-primary"></i> Bản ghi webinar đã sẵn sàng.
                                Đăng
                                ký để xem.
                            @else
                                @if(isset($scheduledTime) && !$scheduledTime->isPast())
                                    <i class="fas fa-calendar-alt me-2 text-primary"></i> Dự kiến diễn ra vào
                                    <strong>{{ $scheduledTime->format('l, F jS, Y') }}</strong> lúc
                                    <strong>{{ $scheduledTime->format('h:i A') }}</strong>
                                @else
                                    <i class="fas fa-clock me-2 text-primary"></i> Thời gian webinar chưa được thiết
                                    lập
                                @endif
                            @endif
                        </p>
                    </div>

                    <div class="text-center mt-4">
                        <button id="registerButton" class="btn cta-button btn-lg">
                            <i class="fas fa-user-plus me-2"></i> Đăng Ký Tham Gia
                        </button>
                    </div>
                @else
                    <div class="alert alert-info mt-4">
                        @if(isset($inWaitingPeriod) && $inWaitingPeriod)

                            <div class="text-center mb-4">
                                <span class="live-badge"><i class="fas fa-circle me-2"></i>Live</span>
                                <h4 class="fw-bold">Webinar đang phát trực tiếp!</h4>
                                <p class="text-muted">Đăng ký ngay để tham gia buổi webinar</p>
                            </div>


                            <div class="webinar-description">
                                <p>
                                    <i class="fas fa-broadcast-tower me-2 text-primary"></i> Webinar đang phát trực
                                    tiếp!
                                    Đăng ký để tham gia ngay.
                                </p>
                            </div>

                            <div class="text-center mt-4">
                                <button id="registerButton" class="btn cta-button btn-lg">
                                    <i class="fas fa-user-plus me-2"></i> Đăng Ký Tham Gia
                                </button>
                            </div>
                        @elseif($startTimeWebinar)
                            Thời gian phát sóng vào lúc: {{$startTimeWebinar->format("H:i d/m/Y")}}
                            <div class="text-center mt-3">
                                <button id="registerButton" class="btn cta-button">
                                    <i class="fas fa-user-plus me-2"></i> Đăng Ký Tham Gia
                                </button>
                            </div>
                        @else
                            <i class="fas fa-info-circle me-2"></i>
                            Không có phiên webinar nào sắp diễn ra.
                            @if($webinar->video_path && isset($showRecording) && $showRecording)
                                <p class="mt-3 mb-0">Bạn có thể xem bản ghi sau khi đăng ký:</p>
                                <div class="text-center mt-3">
                                    <button id="registerButton" class="btn cta-button">
                                        <i class="fas fa-user-plus me-2"></i> Đăng Ký Tham Gia
                                    </button>
                                </div>
                            @else
                                <p class="mt-3 mb-0">Vui lòng quay lại sau khi có lịch phát hoặc liên hệ với ban tổ
                                    chức
                                    để
                                    biết thêm chi tiết.</p>
                            @endif
                        @endif

                    </div>
                @endif
            </div>

            <!-- Form đăng ký ẩn (chỉ hiển thị khi bấm nút) -->
            <div id="registration-form" class="form-section">
                <div class="form-header">
                    <div class="icon-wrapper">
                        <i class="fas fa-user-edit"></i>
                    </div>
                    <h3>Đăng ký tham gia</h3>
                    <p>Hoàn thành thông tin dưới đây để tham gia webinar</p>
                </div>

                @if ($errors->any())
                    <div class="alert alert-danger shadow-sm border-0 mb-4">
                        <ul class="mb-0">
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                <form action="/join/{{ $webinar->join_code }}/form" method="POST" id="join-form">
                    @csrf
                    <input type="hidden" name="debug_info" value="form-submission-{{ time() }}">

                    <!-- UTM Tracking Parameters -->
                    <input type="hidden" name="utm_source" value="{{ request()->get('utm_source') }}">
                    <input type="hidden" name="utm_medium" value="{{ request()->get('utm_medium') }}">
                    <input type="hidden" name="utm_campaign" value="{{ request()->get('utm_campaign') }}">
                    <input type="hidden" name="utm_term" value="{{ request()->get('utm_term') }}">
                    <input type="hidden" name="utm_content" value="{{ request()->get('utm_content') }}">

                    <div class="form-grid">
                        <div class="input-group">
                            <label for="popup-name"
                                   class="@if(isset($webinar->join_settings['name_required']) && $webinar->join_settings['name_required']) required @endif">
                                <i class="fas fa-user text-primary me-2 opacity-75"></i>Họ và tên
                            </label>
                            <input type="text" id="popup-name" name="name" value="{{ old('name') }}"
                                   placeholder="Nhập họ và tên của bạn"
                                   @if(isset($webinar->join_settings['name_required']) && $webinar->join_settings['name_required']) required @endif>
                        </div>

                        <div class="input-group">
                            <label for="popup-phone"
                                   class="@if(isset($webinar->join_settings['phone_required']) && $webinar->join_settings['phone_required']) required @endif">
                                <i class="fas fa-phone-alt text-primary me-2 opacity-75"></i>Số điện thoại
                            </label>
                            <input type="tel" id="popup-phone" name="phone" value="{{ old('phone') }}"
                                   placeholder="Nhập số điện thoại của bạn"
                                   @if(isset($webinar->join_settings['phone_required']) && $webinar->join_settings['phone_required']) required @endif>
                        </div>

                        <div class="input-group full-width">
                            <label for="popup-email"
                                   class="@if(isset($webinar->join_settings['email_required']) && $webinar->join_settings['email_required']) required @endif">
                                <i class="fas fa-envelope text-primary me-2 opacity-75"></i>Email
                            </label>
                            <input type="email" id="popup-email" name="email" value="{{ old('email') }}"
                                   placeholder="Nhập địa chỉ email của bạn"
                                   @if(isset($webinar->join_settings['email_required']) && $webinar->join_settings['email_required']) required @endif>
                        </div>
                    </div>

                    <div class="note mb-4">
                        <i class="fas fa-info-circle me-2"></i>
                        <span class="text-danger">*</span> Trường bắt buộc phải nhập
                    </div>

                    <div class="d-flex gap-3 mt-4">
                        <button type="button" id="backButton" class="btn secondary-button">
                            <i class="fas fa-arrow-left me-2"></i> Quay lại
                        </button>
                        @if(setting('recaptcha_enabled')==="1")
                            <button class="btn cta-button flex-grow-1 g-recaptcha"
                                    data-sitekey="{{ setting('recaptcha_site_key') }}" data-callback='onJoinSubmit'
                                    data-action='submit'>
                                <i class="fas fa-sign-in-alt me-2"></i> Tham gia ngay
                            </button>
                        @else
                            <button type="submit" class="btn cta-button flex-grow-1">
                                <i class="fas fa-sign-in-alt me-2"></i> Tham gia ngay
                            </button>
                        @endif
                    </div>
                </form>
            </div>
        @endif

    </div>
</div>

<!-- Bootstrap JS Bundle with Popper -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

<!-- SweetAlert2 JS -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script src="https://www.google.com/recaptcha/api.js"></script>
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script>
    function onJoinSubmit(token) {
        document.getElementById("join-form").submit();
    }

    function onDirectJoinSubmit(token) {
        document.getElementById("direct-join-form").submit();
    }
</script>
<script>
    $(document).ready(function () {
        // Toggle between webinar details and registration form
        const $registerButton = $('#registerButton');
        const $backButton = $('#backButton');
        const $webinarDetails = $('#webinar-details');
        const $registrationForm = $('#registration-form');
        const $directForm = $('#direct-form');

        // Nếu có form hiển thị trực tiếp, ẩn nút đăng ký
        if ($directForm.length && $directForm.hasClass('show-direct')) {
            $registerButton.hide();
        }

        if ($registerButton.length) {
            $registerButton.on('click', function () {
                $webinarDetails.hide();
                $registrationForm.addClass('active');

                // Smooth scroll to form
                $('html, body').animate({
                    scrollTop: $registrationForm.offset().top - 100
                }, 500);
            });
        }

        if ($backButton.length) {
            $backButton.on('click', function () {
                $webinarDetails.show();
                $registrationForm.removeClass('active');

                // Smooth scroll back to details
                $('html, body').animate({
                    scrollTop: $webinarDetails.offset().top - 100
                }, 500);
            });
        }

        // Virtual viewer count animation with more realistic behavior
        const $viewerCount = $('#viewerCount');
        if ($viewerCount.length) {
            let viewerValue = $viewerCount.text();
            let minCount, maxCount, currentCount;

            // Add a small delay before showing viewer count to new visitors
            setTimeout(() => {
                // Parse the viewer count
                if (viewerValue.includes('-')) {
                    const range = viewerValue.split('-');
                    minCount = parseInt(range[0]);
                    maxCount = parseInt(range[1]);
                    // Generate a random count within the range
                    currentCount = Math.floor(Math.random() * (maxCount - minCount + 1)) + minCount;
                } else {
                    currentCount = parseInt(viewerValue);
                }

                // Show initial count
                $viewerCount.text(currentCount);

                // Simulate viewers joining and leaving
                setInterval(() => {
                    // Get current timestamp to create more organic-looking changes
                    const now = new Date().getTime();

                    // More viewers tend to join in the first few minutes
                    // so weight changes toward positive early on
                    const timeOnPage = (now % 300000) / 300000; // 0-1 based on 5 minute cycle

                    // Generate a weighted random change (-2 to +4)
                    let changeOptions;

                    if (timeOnPage < 0.2) {
                        // First minute: higher chance of viewers joining
                        changeOptions = [-1, 0, 1, 1, 2, 2, 3];
                    } else if (timeOnPage > 0.8) {
                        // Last minute: higher chance of viewers leaving
                        changeOptions = [-2, -1, -1, 0, 0, 1];
                    } else {
                        // Middle period: balanced changes
                        changeOptions = [-1, -1, 0, 0, 0, 1, 1];
                    }

                    // Select a random change from our weighted options
                    const change = changeOptions[Math.floor(Math.random() * changeOptions.length)];

                    // Apply the change with constraints
                    if (viewerValue.includes('-')) {
                        // Stay within minimum and maximum range
                        currentCount = Math.max(minCount, Math.min(maxCount, currentCount + change));
                    } else {
                        // Never go below 1 viewer
                        currentCount = Math.max(1, currentCount + change);
                    }

                    // Occasionally show "someone joined" message for user engagement
                    if (change > 0 && Math.random() < 0.3) {
                        const $viewerCountElement = $('.viewers-count');

                        // Create a temporary notification that fades out
                        const $notification = $('<div>', {
                            class: 'viewer-joined',
                            html: '<i class="fas fa-user-plus text-success me-2"></i> Người xem mới vừa tham gia',
                            css: {
                                position: 'absolute',
                                top: '105%',
                                left: 0,
                                fontSize: '0.8rem',
                                opacity: 0,
                                transition: 'opacity 0.5s ease'
                            }
                        });

                        $viewerCountElement.css('position', 'relative').append($notification);

                        // Show and then hide the notification
                        setTimeout(() => {
                            $notification.css('opacity', '1');
                            setTimeout(() => {
                                $notification.css('opacity', '0');
                                setTimeout(() => {
                                    $notification.remove();
                                }, 500);
                            }, 2000);
                        }, 10);
                    }

                    // Update the display
                    $viewerCount.text(currentCount);
                }, 8000 + (Math.random() * 4000)); // Randomize interval 8-12 seconds
            }, 1500); // Initial delay
        }

        // Countdown timer
        const $countdown = $('#countdown');
        if ($countdown.length && $countdown.data('target')) {
            const targetTime = parseInt($countdown.data('target'));

            function updateCountdown() {
                const now = Math.floor(Date.now() / 1000);
                const diff = targetTime - now;

                if (diff <= 0) {
                    // Time's up
                    $('#days, #hours, #minutes, #seconds').text('0');

                    // Reload page to update status
                    location.reload();
                    return;
                }

                const days = Math.floor(diff / 86400);
                const hours = Math.floor((diff % 86400) / 3600);
                const minutes = Math.floor((diff % 3600) / 60);
                const seconds = Math.floor(diff % 60);

                $('#days').text(days);
                $('#hours').text(hours);
                $('#minutes').text(minutes);
                $('#seconds').text(seconds);
            }

            updateCountdown();
            setInterval(updateCountdown, 1000);
        }

        // Reminder button
        const $remindButton = $('#remindButton');
        if ($remindButton.length) {
            $remindButton.on('click', function () {
                if (Notification.permission !== 'granted') {
                    Notification.requestPermission().then(function (permission) {
                        if (permission === 'granted') {
                            setReminder();
                        }
                    });
                } else {
                    setReminder();
                }
            });
        }

        function setReminder() {
            $remindButton
                .html('<i class="fas fa-check me-2"></i> Đã đặt nhắc nhở')
                .removeClass('btn-outline-primary')
                .addClass('btn-success')
                .prop('disabled', true);

            Swal.fire({
                icon: 'success',
                title: 'Đã đặt nhắc nhở',
                text: 'Bạn sẽ được thông báo khi phòng chờ mở.',
                timer: 3000,
                timerProgressBar: true
            });
        }
    });
</script>

@include('sweetalert::alert')

<script>
    // WebView Detection Functions
    function isWebView() {
        const userAgent = navigator.userAgent.toLowerCase();

        // Check for common browser indicators
        const isChrome = userAgent.includes('chrome') && !userAgent.includes('edg');
        const isSafari = userAgent.includes('safari') && !userAgent.includes('chrome');
        const isFirefox = userAgent.includes('firefox');
        const isEdge = userAgent.includes('edg');

        // If it's a regular browser, return false
        if (isChrome || isSafari || isFirefox || isEdge) {
            return {
                isInWebView: false,
                isIOS: false
            };
        }

        // Check for WebView indicators
        const isAndroidWebView = userAgent.includes('wv');
        const isIOSWebView = /iphone|ipad|ipod/.test(userAgent) &&
            !window.MSStream &&
            !userAgent.includes('safari') &&
            (window.webkit && window.webkit.messageHandlers);

        const isInWebView = isAndroidWebView || isIOSWebView ||
            window.ReactNativeWebView ||
            (window.webkit && window.webkit.messageHandlers);

        return {
            isInWebView,
            isIOS: isIOSWebView
        };
    }

    function openInBrowser(url) {
        const detection = isWebView();

        if (detection.isIOS) {
            // Remove protocol for iOS URL schemes
            const urlWithoutProtocol = url.replace(/^https?:\/\//, '');

            // Try Chrome first
            window.location.href = `x-safari-https://${urlWithoutProtocol}`;

            // Fallback to Safari after a short delay

            // Final fallback to Safari
            setTimeout(() => {
                window.location.href = url;
            }, 200);
        } else {
            // For Android and other platforms
            window.open(url, '_system');
        }
    }

    function showWebViewNotice() {
        const detection = isWebView();
        if (detection.isInWebView) {
            // Show modal and hide register button and direct form using jQuery
            $('#webview-modal-overlay').show();
            $('#registerButton').hide();
            $('#direct-form').hide();

            const currentUrl = window.location.href;
            const browserButton = $('#open-in-browser');

            browserButton.on('click', function (e) {
                e.preventDefault();
                openInBrowser(currentUrl);
            });

            // Close modal when clicking overlay (optional)
            $('#webview-modal-overlay').on('click', function (e) {
                if (e.target === this) {
                    $(this).hide();
                }
            });
        }
    }

    // Run the detection when page loads
    $(document).ready(function () {
        showWebViewNotice();
    });
</script>
<script>
    function validateVietnamesePhone(phone) {
        // Regex cho số điện thoại Việt Nam
        const vietnamesePhoneRegex = /^(0|\+84)(\s?)((3[2-9])|(5[689])|(7[06-9])|(8[1-689])|(9[0-46-9]))(\d)(\s?\d{3})(\s?\d{3})$/;
        return vietnamesePhoneRegex.test(phone);
    }

    $(document).ready(function () {
        $('form').on('submit', function (e) {
            const phoneNumber = $('#phone').val().trim();
            // Xóa thông báo lỗi cũ nếu có
            $('.phone-error').remove();
            @if(isset($webinar->join_settings['phone_required']) && $webinar->join_settings['phone_required'])
            if (!validateVietnamesePhone(phoneNumber)) {
                e.preventDefault();
                $('#phone').after('<div class="text-danger mt-2 phone-error">Vui lòng nhập đúng số điện thoại</div>');
                return false;
            }
            @else
            if (phoneNumber.length > 0) {
                if (!validateVietnamesePhone(phoneNumber)) {
                    e.preventDefault();
                    $('#phone').after('<div class="text-danger mt-2 phone-error">Vui lòng nhập đúng số điện thoại</div>');
                    return false;
                }
            }
            @endif
        });

        // Xóa thông báo lỗi khi người dùng bắt đầu nhập lại
        $('#phone').on('input', function () {
            $('.phone-error').remove();
        });
    });
</script>
</body>
</html>
