<?php

namespace Tests\Feature;

use App\Models\GlobalParticipant;
use App\Models\Webinar;
use App\Models\WebinarParticipant;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class GlobalParticipantTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_can_auto_join_webinar_with_existing_global_participant()
    {
        // Create a webinar
        $webinar = Webinar::factory()->create([
            'join_code' => 'test123',
            'title' => 'Test Webinar'
        ]);

        // Create a global participant (simulating previous registration)
        $globalParticipant = GlobalParticipant::create([
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'phone' => '0123456789',
            'ip_address' => '127.0.0.1',
            'expires_at' => Carbon::now()->addMonths(6)
        ]);

        // Simulate request with same IP and set cookie
        $response = $this->withCookie('global_participant_id', $globalParticipant->id)
            ->get("/join/{$webinar->join_code}");

        // Should redirect to enter page
        $response->assertRedirect("/join/{$webinar->join_code}/enter");

        // Check that a webinar participant was created
        $this->assertDatabaseHas('webinar_participants', [
            'webinar_id' => $webinar->id,
            'email' => '<EMAIL>',
            'phone' => '0123456789'
        ]);
    }

    public function test_expired_global_participant_does_not_auto_join()
    {
        // Create a webinar
        $webinar = Webinar::factory()->create([
            'join_code' => 'test123'
        ]);

        // Create an expired global participant
        $globalParticipant = GlobalParticipant::create([
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'phone' => '0123456789',
            'ip_address' => '127.0.0.1',
            'expires_at' => Carbon::now()->subDays(1) // Expired
        ]);

        // Simulate request
        $response = $this->withCookie('global_participant_id', $globalParticipant->id)
            ->get("/join/{$webinar->join_code}");

        // Should show join page, not redirect to enter
        $response->assertStatus(200);
        $response->assertViewIs('join');
    }

    public function test_global_participant_expiration_is_extended_on_new_registration()
    {
        $webinar = Webinar::factory()->create([
            'join_code' => 'test123'
        ]);

        // Create a global participant that expires soon
        $globalParticipant = GlobalParticipant::create([
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'phone' => '0123456789',
            'ip_address' => '127.0.0.1',
            'expires_at' => Carbon::now()->addDays(30) // Expires in 30 days
        ]);

        $originalExpiration = $globalParticipant->expires_at;

        // Register for a new webinar
        $response = $this->post("/join/{$webinar->join_code}/form", [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'phone' => '0123456789'
        ]);

        // Check that expiration was extended
        $globalParticipant->refresh();
        $this->assertTrue($globalParticipant->expires_at->gt($originalExpiration));
        $this->assertTrue($globalParticipant->expires_at->gte(Carbon::now()->addMonths(6)->subMinute()));
    }
}
