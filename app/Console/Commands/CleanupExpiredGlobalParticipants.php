<?php

namespace App\Console\Commands;

use App\Models\GlobalParticipant;
use Carbon\Carbon;
use Illuminate\Console\Command;

class CleanupExpiredGlobalParticipants extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'global-participants:cleanup';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up expired global participants (older than 6 months)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting cleanup of expired global participants...');

        $expiredCount = GlobalParticipant::where('expires_at', '<', Carbon::now())->count();

        if ($expiredCount === 0) {
            $this->info('No expired global participants found.');
            return 0;
        }

        $this->info("Found {$expiredCount} expired global participants.");

        if ($this->confirm('Do you want to delete these expired records?')) {
            $deleted = GlobalParticipant::where('expires_at', '<', Carbon::now())->delete();
            $this->info("Successfully deleted {$deleted} expired global participants.");
        } else {
            $this->info('Cleanup cancelled.');
        }

        return 0;
    }
}
