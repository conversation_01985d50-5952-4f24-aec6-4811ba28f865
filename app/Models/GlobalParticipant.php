<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class GlobalParticipant extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'phone',
        'ip_address',
        'expires_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'expires_at' => 'datetime',
    ];

    /**
     * Scope to get only non-expired participants
     */
    public function scopeNotExpired($query)
    {
        return $query->where('expires_at', '>', Carbon::now());
    }

    /**
     * Check if the participant is expired
     */
    public function isExpired(): bool
    {
        return $this->expires_at->isPast();
    }

    /**
     * Extend the expiration date by 6 months
     */
    public function extendExpiration(): void
    {
        $this->update([
            'expires_at' => Carbon::now()->addMonths(6)
        ]);
    }

    /**
     * Find or create a global participant
     */
    public static function findOrCreateByEmailOrPhone(?string $email, ?string $phone, array $data = []): ?self
    {
        if (!$email && !$phone) {
            return null;
        }

        $participant = null;

        // Try to find by email first
        if ($email) {
            $participant = self::where('email', $email)->notExpired()->first();
        }

        // If not found by email, try phone
        if (!$participant && $phone) {
            $participant = self::where('phone', $phone)->notExpired()->first();
        }

        if ($participant) {
            // Update existing participant and extend expiration
            $participant->update(array_merge($data, [
                'expires_at' => Carbon::now()->addMonths(6)
            ]));
            return $participant;
        }

        // Create new participant
        return self::create(array_merge($data, [
            'email' => $email,
            'phone' => $phone,
            'expires_at' => Carbon::now()->addMonths(6)
        ]));
    }
}
